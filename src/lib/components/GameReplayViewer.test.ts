/**
 * Tests for GameReplayViewer component
 *
 * This test suite verifies that the replay functionality correctly displays
 * the original letters that were clicked during each move, not the letters
 * that appear at those positions after the move was played.
 */

import { describe, it, expect, beforeEach } from 'vitest';
import type { EnhancedMove, Tile } from '$lib/types';

describe('GameReplayViewer Display Logic', () => {
	let mockMoves: EnhancedMove[];
	let mockInitialBoard: Tile[][];

	beforeEach(() => {
		// Create a mock initial board state
		mockInitialBoard = [
			[
				{ letter: 'H', base: 4, letterMult: 1, wordMult: 1, row: 0, col: 0 },
				{ letter: 'E', base: 1, letterMult: 1, wordMult: 1, row: 0, col: 1 },
				{ letter: 'L', base: 1, letterMult: 1, wordMult: 1, row: 0, col: 2 },
				{ letter: 'L', base: 1, letterMult: 1, wordMult: 1, row: 0, col: 3 },
				{ letter: 'O', base: 1, letterMult: 1, wordMult: 1, row: 0, col: 4 }
			],
			[
				{ letter: 'W', base: 4, letterMult: 1, wordMult: 1, row: 1, col: 0 },
				{ letter: 'O', base: 1, letterMult: 1, wordMult: 1, row: 1, col: 1 },
				{ letter: 'R', base: 1, letterMult: 1, wordMult: 1, row: 1, col: 2 },
				{ letter: 'L', base: 1, letterMult: 1, wordMult: 1, row: 1, col: 3 },
				{ letter: 'D', base: 2, letterMult: 1, wordMult: 1, row: 1, col: 4 }
			],
			[
				{ letter: 'T', base: 1, letterMult: 1, wordMult: 1, row: 2, col: 0 },
				{ letter: 'E', base: 1, letterMult: 1, wordMult: 1, row: 2, col: 1 },
				{ letter: 'S', base: 1, letterMult: 1, wordMult: 1, row: 2, col: 2 },
				{ letter: 'T', base: 1, letterMult: 1, wordMult: 1, row: 2, col: 3 },
				{ letter: 'S', base: 1, letterMult: 1, wordMult: 1, row: 2, col: 4 }
			],
			[
				{ letter: 'A', base: 1, letterMult: 1, wordMult: 1, row: 3, col: 0 },
				{ letter: 'B', base: 3, letterMult: 1, wordMult: 1, row: 3, col: 1 },
				{ letter: 'C', base: 3, letterMult: 1, wordMult: 1, row: 3, col: 2 },
				{ letter: 'D', base: 2, letterMult: 1, wordMult: 1, row: 3, col: 3 },
				{ letter: 'E', base: 1, letterMult: 1, wordMult: 1, row: 3, col: 4 }
			],
			[
				{ letter: 'F', base: 4, letterMult: 1, wordMult: 1, row: 4, col: 0 },
				{ letter: 'G', base: 2, letterMult: 1, wordMult: 1, row: 4, col: 1 },
				{ letter: 'H', base: 4, letterMult: 1, wordMult: 1, row: 4, col: 2 },
				{ letter: 'I', base: 1, letterMult: 1, wordMult: 1, row: 4, col: 3 },
				{ letter: 'J', base: 8, letterMult: 1, wordMult: 1, row: 4, col: 4 }
			]
		];

		// Create a mock board state after the first move (tiles have fallen down and new ones generated)
		const boardAfterMove1: Tile[][] = [
			[
				{ letter: 'X', base: 8, letterMult: 1, wordMult: 1, row: 0, col: 0 }, // New tile
				{ letter: 'Y', base: 4, letterMult: 1, wordMult: 1, row: 0, col: 1 }, // New tile
				{ letter: 'Z', base: 10, letterMult: 1, wordMult: 1, row: 0, col: 2 }, // New tile
				{ letter: 'Q', base: 10, letterMult: 1, wordMult: 1, row: 0, col: 3 }, // New tile
				{ letter: 'K', base: 5, letterMult: 1, wordMult: 1, row: 0, col: 4 } // New tile
			],
			[
				{ letter: 'W', base: 4, letterMult: 1, wordMult: 1, row: 1, col: 0 }, // Fell down
				{ letter: 'O', base: 1, letterMult: 1, wordMult: 1, row: 1, col: 1 }, // Fell down
				{ letter: 'R', base: 1, letterMult: 1, wordMult: 1, row: 1, col: 2 }, // Fell down
				{ letter: 'L', base: 1, letterMult: 1, wordMult: 1, row: 1, col: 3 }, // Fell down
				{ letter: 'D', base: 2, letterMult: 1, wordMult: 1, row: 1, col: 4 } // Fell down
			],
			[
				{ letter: 'T', base: 1, letterMult: 1, wordMult: 1, row: 2, col: 0 },
				{ letter: 'E', base: 1, letterMult: 1, wordMult: 1, row: 2, col: 1 },
				{ letter: 'S', base: 1, letterMult: 1, wordMult: 1, row: 2, col: 2 },
				{ letter: 'T', base: 1, letterMult: 1, wordMult: 1, row: 2, col: 3 },
				{ letter: 'S', base: 1, letterMult: 1, wordMult: 1, row: 2, col: 4 }
			],
			[
				{ letter: 'A', base: 1, letterMult: 1, wordMult: 1, row: 3, col: 0 },
				{ letter: 'B', base: 3, letterMult: 1, wordMult: 1, row: 3, col: 1 },
				{ letter: 'C', base: 3, letterMult: 1, wordMult: 1, row: 3, col: 2 },
				{ letter: 'D', base: 2, letterMult: 1, wordMult: 1, row: 3, col: 3 },
				{ letter: 'E', base: 1, letterMult: 1, wordMult: 1, row: 3, col: 4 }
			],
			[
				{ letter: 'F', base: 4, letterMult: 1, wordMult: 1, row: 4, col: 0 },
				{ letter: 'G', base: 2, letterMult: 1, wordMult: 1, row: 4, col: 1 },
				{ letter: 'H', base: 4, letterMult: 1, wordMult: 1, row: 4, col: 2 },
				{ letter: 'I', base: 1, letterMult: 1, wordMult: 1, row: 4, col: 3 },
				{ letter: 'J', base: 8, letterMult: 1, wordMult: 1, row: 4, col: 4 }
			]
		];

		// Create mock moves
		mockMoves = [
			{
				word: 'HELLO',
				score: 8,
				positions: [
					[0, 0],
					[0, 1],
					[0, 2],
					[0, 3],
					[0, 4]
				], // Top row
				boardBefore: mockInitialBoard,
				boardAfter: boardAfterMove1
			}
		];
	});

	describe('displayBoard logic', () => {
		it('should show original letters at selected positions during replay', () => {
			// Simulate the displayBoard logic from GameReplayViewer
			const currentMoveIndex = 0; // Viewing first move
			const move = mockMoves[0];
			const currentBoard = move.boardAfter!; // Board after move (with new tiles)

			// Create hybrid board: current board state but with original letters at selected positions
			const hybridBoard = currentBoard.map((row, rowIndex) =>
				row.map((tile, colIndex) => {
					// Check if this position was selected in the current move
					const isSelectedPosition = move.positions.some(
						([r, c]) => r === rowIndex && c === colIndex
					);

					if (isSelectedPosition && move.boardBefore) {
						// Use the original tile from before the move was played
						return move.boardBefore[rowIndex][colIndex];
					} else {
						// Use the current tile
						return tile;
					}
				})
			);

			// Verify that selected positions show original letters
			expect(hybridBoard[0][0].letter).toBe('H'); // Original letter, not 'X'
			expect(hybridBoard[0][1].letter).toBe('E'); // Original letter, not 'Y'
			expect(hybridBoard[0][2].letter).toBe('L'); // Original letter, not 'Z'
			expect(hybridBoard[0][3].letter).toBe('L'); // Original letter, not 'Q'
			expect(hybridBoard[0][4].letter).toBe('O'); // Original letter, not 'K'

			// Verify that non-selected positions show current letters (from boardAfter of the CURRENT move)
			expect(hybridBoard[1][0].letter).toBe('W'); // Current letter (fell down from move 1)
			expect(hybridBoard[2][0].letter).toBe('T'); // Current letter (unchanged from move 1)
			expect(hybridBoard[4][4].letter).toBe('J'); // Current letter (unchanged from move 1)
		});

		it('should correctly handle multiple moves with proper board states', () => {
			// Create a second move to test the issue described
			const boardAfterMove2: Tile[][] = [
				[
					{ letter: 'P', base: 3, letterMult: 1, wordMult: 1, row: 0, col: 0 }, // Different from move 1
					{ letter: 'Q', base: 10, letterMult: 1, wordMult: 1, row: 0, col: 1 },
					{ letter: 'R', base: 1, letterMult: 1, wordMult: 1, row: 0, col: 2 },
					{ letter: 'S', base: 1, letterMult: 1, wordMult: 1, row: 0, col: 3 },
					{ letter: 'T', base: 1, letterMult: 1, wordMult: 1, row: 0, col: 4 }
				],
				// ... rest of board
				...mockMoves[0].boardAfter!.slice(1)
			];

			const move2: EnhancedMove = {
				word: 'WORLD',
				score: 9,
				positions: [
					[1, 0],
					[1, 1],
					[1, 2],
					[1, 3],
					[1, 4]
				], // Second row
				boardBefore: mockMoves[0].boardAfter!, // Board after move 1
				boardAfter: boardAfterMove2
			};

			const movesWithTwo = [mockMoves[0], move2];

			// Test viewing move 1 (index 0)
			const currentMoveIndex = 0;
			const move = movesWithTwo[0];
			const currentBoard = move.boardAfter!; // Should be board after move 1, NOT move 2

			// Verify non-selected positions show board state after move 1, not move 2
			expect(currentBoard[0][0].letter).toBe('X'); // From move 1, not 'P' from move 2
			expect(currentBoard[0][1].letter).toBe('Y'); // From move 1, not 'Q' from move 2
		});

		it('should simulate the exact bug scenario and verify the fix', () => {
			// Create a second move to test the issue described
			const boardAfterMove2: Tile[][] = [
				[
					{ letter: 'P', base: 3, letterMult: 1, wordMult: 1, row: 0, col: 0 }, // Move 2 state
					{ letter: 'Q', base: 10, letterMult: 1, wordMult: 1, row: 0, col: 1 }, // Move 2 state
					{ letter: 'R', base: 1, letterMult: 1, wordMult: 1, row: 0, col: 2 }, // Move 2 state
					{ letter: 'S', base: 1, letterMult: 1, wordMult: 1, row: 0, col: 3 }, // Move 2 state
					{ letter: 'T', base: 1, letterMult: 1, wordMult: 1, row: 0, col: 4 } // Move 2 state
				],
				[
					{ letter: 'A', base: 1, letterMult: 1, wordMult: 1, row: 1, col: 0 }, // Move 2 state
					{ letter: 'B', base: 3, letterMult: 1, wordMult: 1, row: 1, col: 1 }, // Move 2 state
					{ letter: 'C', base: 3, letterMult: 1, wordMult: 1, row: 1, col: 2 }, // Move 2 state
					{ letter: 'D', base: 2, letterMult: 1, wordMult: 1, row: 1, col: 3 }, // Move 2 state
					{ letter: 'E', base: 1, letterMult: 1, wordMult: 1, row: 1, col: 4 } // Move 2 state
				],
				...mockMoves[0].boardAfter!.slice(2)
			];

			const move2: EnhancedMove = {
				word: 'WORLD',
				score: 9,
				positions: [
					[1, 0],
					[1, 1],
					[1, 2],
					[1, 3],
					[1, 4]
				], // Second row
				boardBefore: mockMoves[0].boardAfter!, // Board after move 1
				boardAfter: boardAfterMove2
			};

			const movesWithTwo = [mockMoves[0], move2];

			// Simulate the NEW GameReplayViewer logic for viewing move 1 (index 0)
			const currentMoveIndex = 0;
			const move = movesWithTwo[currentMoveIndex];

			// This simulates the new displayBoard logic
			const boardAfterCurrentMove = move.boardAfter!;
			const hybridBoard = boardAfterCurrentMove.map((row, rowIndex) =>
				row.map((tile, colIndex) => {
					const isSelectedPosition = move.positions.some(
						([r, c]) => r === rowIndex && c === colIndex
					);

					if (isSelectedPosition && move.boardBefore) {
						// Selected tiles should show original letters from move 1's boardBefore
						return move.boardBefore[rowIndex][colIndex];
					} else {
						// Non-selected tiles should show board state from move 1's boardAfter (THIS move, not next)
						return tile;
					}
				})
			);

			// Test the expected behavior:
			// Selected tiles (top row) should show original letters H-E-L-L-O
			expect(hybridBoard[0][0].letter).toBe('H'); // Original from boardBefore
			expect(hybridBoard[0][1].letter).toBe('E'); // Original from boardBefore
			expect(hybridBoard[0][2].letter).toBe('L'); // Original from boardBefore
			expect(hybridBoard[0][3].letter).toBe('L'); // Original from boardBefore
			expect(hybridBoard[0][4].letter).toBe('O'); // Original from boardBefore

			// Non-selected tiles should show board state from move 1's boardAfter (NOT move 2's)
			expect(hybridBoard[1][0].letter).toBe('W'); // From move 1's boardAfter, not 'A' from move 2
			expect(hybridBoard[1][1].letter).toBe('O'); // From move 1's boardAfter, not 'B' from move 2
			expect(hybridBoard[2][0].letter).toBe('T'); // From move 1's boardAfter (unchanged)

			// Verify that we're NOT showing move 2's board state for non-selected tiles
			expect(hybridBoard[1][0].letter).not.toBe('A'); // Should not be from move 2
			expect(hybridBoard[1][1].letter).not.toBe('B'); // Should not be from move 2
		});

		it('should return current board when not viewing a specific move', () => {
			const currentMoveIndex = -1; // Initial state
			const currentBoard = mockInitialBoard;

			// When not viewing a specific move, should just return the current board
			const displayBoard = currentBoard;

			expect(displayBoard).toBe(mockInitialBoard);
		});

		it('should handle missing boardBefore data gracefully', () => {
			const moveWithoutBoardBefore: EnhancedMove = {
				word: 'TEST',
				score: 4,
				positions: [
					[0, 0],
					[0, 1],
					[0, 2],
					[0, 3]
				],
				// No boardBefore data
				boardAfter: mockMoves[0].boardAfter
			};

			const currentBoard = moveWithoutBoardBefore.boardAfter!;

			// Should fall back to current board when boardBefore is missing
			const hybridBoard = currentBoard; // Fallback behavior

			expect(hybridBoard).toBe(currentBoard);
		});
	});

	describe('replay navigation', () => {
		it('should correctly identify selected positions for highlighting', () => {
			const move = mockMoves[0];
			const selectedPositions = move.positions;

			expect(selectedPositions).toEqual([
				[0, 0],
				[0, 1],
				[0, 2],
				[0, 3],
				[0, 4]
			]);
		});

		it('should show correct move information', () => {
			const move = mockMoves[0];

			expect(move.word).toBe('HELLO');
			expect(move.score).toBe(8);
			expect(move.positions).toHaveLength(5);
		});
	});
});
